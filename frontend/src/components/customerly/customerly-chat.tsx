'use client';

import { useEffect } from 'react';
import { useCustomerly } from 'react-live-chat-customerly';
import { useAuth } from '@/components/AuthProvider';

export function CustomerlyChat() {
  const { load, update, registerCallback } = useCustomerly();
  const { user } = useAuth();

  useEffect(() => {
    // Load Customerly chat when component mounts
    if (user) {
      load({
        user_id: user.id,
        email: user.email || '',
        name: user.user_metadata?.full_name || user.email || 'User',
        attributes: {
          signup_date: user.created_at ? Math.floor(new Date(user.created_at).getTime() / 1000) : undefined,
          user_role: user.user_metadata?.role || 'user',
          last_sign_in: user.last_sign_in_at ? Math.floor(new Date(user.last_sign_in_at).getTime() / 1000) : undefined,
        },
        // Customize chat appearance
        accentColor: '#3b82f6', // Blue color to match your theme
        contrastColor: '#ffffff',
        visible: true,
        visibleOnMobile: true,
        attachmentsAvailable: true,
        autodetectLocale: true,
      });
    } else {
      // Load for anonymous users
      load({
        visible: true,
        visibleOnMobile: true,
        attachmentsAvailable: true,
        autodetectLocale: true,
        accentColor: '#3b82f6',
        contrastColor: '#ffffff',
      });
    }

    // Register callbacks for analytics and tracking
    registerCallback({
      type: 'onLeadGenerated',
      function: (email: string) => {
        // Track lead generation for analytics
        // TODO: Integrate with your analytics platform
        console.log('New lead generated:', email);
      }
    });

    registerCallback({
      type: 'onNewConversation',
      function: (message: string, attachments?: any) => {
        // Track new conversations for analytics
        // TODO: Integrate with your analytics platform
        console.log('New conversation started:', message, attachments);
      }
    });

    registerCallback({
      type: 'onChatOpened',
      function: () => {
        // Track chat opens for analytics
        // TODO: Integrate with your analytics platform
        console.log('Chat opened');
      }
    });

  }, [user]); // Remove load and registerCallback from dependencies to avoid re-registering

  // Update user data when user changes
  useEffect(() => {
    if (user) {
      update({
        user_id: user.id,
        email: user.email || '',
        name: user.user_metadata?.full_name || user.email || 'User',
        attributes: {
          signup_date: user.created_at ? Math.floor(new Date(user.created_at).getTime() / 1000) : undefined,
          user_role: user.user_metadata?.role || 'user',
          last_sign_in: user.last_sign_in_at ? Math.floor(new Date(user.last_sign_in_at).getTime() / 1000) : undefined,
        },
      });
    }
  }, [user]); // Remove update from dependencies to avoid infinite loops

  // This component doesn't render anything visible - it just manages the chat
  return null;
}
