'use client';

import { useEffect } from 'react';
import { useCustomerly } from 'react-live-chat-customerly';
import { useAuth } from '@/components/AuthProvider';

export function CustomerlyChat() {
  const { load, update, registerCallback } = useCustomerly();
  const { user } = useAuth();

  // Debug logging
  console.log('CustomerlyChat component mounted');
  console.log('Customerly Project ID:', process.env.NEXT_PUBLIC_CUSTOMERLY_PROJECT_ID);
  console.log('User:', user);

  useEffect(() => {
    console.log('CustomerlyChat useEffect triggered');

    // Load Customerly chat when component mounts
    if (user) {
      console.log('Loading Customerly for authenticated user:', user.email);
      load({
        user_id: user.id,
        email: user.email || '',
        name: user.user_metadata?.full_name || user.email || 'User',
        attributes: {
          signup_date: user.created_at ? Math.floor(new Date(user.created_at).getTime() / 1000) : undefined,
          user_role: user.user_metadata?.role || 'user',
          last_sign_in: user.last_sign_in_at ? Math.floor(new Date(user.last_sign_in_at).getTime() / 1000) : undefined,
        },
        // Customize chat appearance
        accentColor: '#3b82f6', // Blue color to match your theme
        contrastColor: '#ffffff',
        visible: true,
        visibleOnMobile: true,
        attachmentsAvailable: true,
        autodetectLocale: true,
      });
    } else {
      console.log('Loading Customerly for anonymous user');
      // Load for anonymous users
      load({
        visible: true,
        visibleOnMobile: true,
        attachmentsAvailable: true,
        autodetectLocale: true,
        accentColor: '#3b82f6',
        contrastColor: '#ffffff',
      });
    }

    // Register callbacks for analytics and tracking
    registerCallback({
      type: 'onLeadGenerated',
      function: (email: string) => {
        console.log('New lead generated:', email);
        // You can add analytics tracking here
        // Example: analytics.track('Lead Generated', { email });
      }
    });

    registerCallback({
      type: 'onNewConversation',
      function: (message: string, attachments?: any) => {
        console.log('New conversation started:', message, attachments);
        // Track new conversations for analytics
        // Example: analytics.track('Chat Conversation Started', { message, attachments });
      }
    });

    registerCallback({
      type: 'onChatOpened',
      function: () => {
        console.log('Chat opened');
        // Track chat opens
        // Example: analytics.track('Chat Opened');
      }
    });

  }, [user]); // Remove load and registerCallback from dependencies to avoid re-registering

  // Update user data when user changes
  useEffect(() => {
    if (user) {
      update({
        user_id: user.id,
        email: user.email || '',
        name: user.user_metadata?.full_name || user.email || 'User',
        attributes: {
          signup_date: user.created_at ? Math.floor(new Date(user.created_at).getTime() / 1000) : undefined,
          user_role: user.user_metadata?.role || 'user',
          last_sign_in: user.last_sign_in_at ? Math.floor(new Date(user.last_sign_in_at).getTime() / 1000) : undefined,
        },
      });
    }
  }, [user]); // Remove update from dependencies to avoid infinite loops

  // This component doesn't render anything visible - it just manages the chat
  return null;
}
