'use client';

import { useCustomerly } from 'react-live-chat-customerly';
import { But<PERSON> } from '@/components/ui/button';
import { MessageCircle, X, Eye, EyeOff } from 'lucide-react';

interface ChatControlsProps {
  className?: string;
}

export function ChatControls({ className }: ChatControlsProps) {
  const { open, close, show, hide, showNewMessage } = useCustomerly();

  const handleOpenWithMessage = () => {
    showNewMessage("Hi! I'm interested in learning more about <PERSON>.");
  };

  return (
    <div className={`flex gap-2 ${className}`}>
      <Button
        variant="outline"
        size="sm"
        onClick={open}
        className="flex items-center gap-2"
      >
        <MessageCircle className="h-4 w-4" />
        Open Chat
      </Button>
      
      <Button
        variant="outline"
        size="sm"
        onClick={close}
        className="flex items-center gap-2"
      >
        <X className="h-4 w-4" />
        Close
      </Button>
      
      <Button
        variant="outline"
        size="sm"
        onClick={show}
        className="flex items-center gap-2"
      >
        <Eye className="h-4 w-4" />
        Show
      </Button>
      
      <Button
        variant="outline"
        size="sm"
        onClick={hide}
        className="flex items-center gap-2"
      >
        <EyeOff className="h-4 w-4" />
        Hide
      </Button>
      
      <Button
        variant="default"
        size="sm"
        onClick={handleOpenWithMessage}
        className="flex items-center gap-2"
      >
        <MessageCircle className="h-4 w-4" />
        Start Conversation
      </Button>
    </div>
  );
}
