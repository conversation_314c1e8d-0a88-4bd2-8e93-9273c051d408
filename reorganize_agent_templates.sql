-- <PERSON><PERSON> Script to Reorganize agent_templates table
-- Reorders agents: 1st Research, 2nd Sales & Outreach, 3rd Productivity, 4th Social Media, 5th Dev

-- Method 1: If you want to add a display_order column for future ordering
ALTER TABLE agent_templates ADD COLUMN IF NOT EXISTS display_order INTEGER;

-- Update display_order based on agent names
UPDATE agent_templates
SET display_order = CASE
    WHEN name = 'Research & Analysis Agent' THEN 1
    WHEN name = 'Sales & Outreach Assistant' THEN 2
    WHEN name = 'Productivity Hub' THEN 3
    WHEN name = 'Social Media Manager' THEN 4
    WHEN name = 'Code & Dev Assistant' THEN 5
    ELSE 99 -- For any other agents
END;

-- Method 2: If you want to update the created_at timestamps to change natural order
-- (Use this if your table orders by created_at by default)

-- First, let's see current agents and their order
SELECT name, template_id, created_at FROM agent_templates ORDER BY created_at;

-- Update created_at timestamps to reflect desired order
-- (Adjust the base timestamp as needed - using a recent date)
UPDATE agent_templates
SET created_at = CASE
    WHEN name = 'Research & Analysis Agent' THEN '2024-01-01 10:00:00'::timestamp
    WHEN name = 'Sales & Outreach Assistant' THEN '2024-01-01 10:01:00'::timestamp
    WHEN name = 'Productivity Hub' THEN '2024-01-01 10:02:00'::timestamp
    WHEN name = 'Social Media Manager' THEN '2024-01-01 10:03:00'::timestamp
    WHEN name = 'Code & Dev Assistant' THEN '2024-01-01 10:04:00'::timestamp
    ELSE created_at -- Keep original timestamp for other agents
END;

-- Method 3: If you want to completely reorder by updating template_ids
-- (More complex - creates new UUIDs in sequence)

-- Create a mapping table for the new order
CREATE TEMP TABLE agent_reorder_mapping AS
SELECT
    template_id as old_id,
    name,
    ROW_NUMBER() OVER (ORDER BY
        CASE
            WHEN name = 'Research & Analysis Agent' THEN 1
            WHEN name = 'Sales & Outreach Assistant' THEN 2
            WHEN name = 'Productivity Hub' THEN 3
            WHEN name = 'Social Media Manager' THEN 4
            WHEN name = 'Code & Dev Assistant' THEN 5
            ELSE 99
        END
    ) as new_position
FROM agent_templates;

-- Verify the new order will be correct
SELECT
    new_position,
    name,
    old_id
FROM agent_reorder_mapping
ORDER BY new_position;

-- Final verification query - use this to check results
SELECT
    CASE
        WHEN display_order IS NOT NULL THEN display_order
        ELSE ROW_NUMBER() OVER (ORDER BY created_at)
    END as position,
    name,
    template_id,
    created_at,
    display_order
FROM agent_templates
ORDER BY
    CASE
        WHEN display_order IS NOT NULL THEN display_order
        ELSE ROW_NUMBER() OVER (ORDER BY created_at)
    END;
